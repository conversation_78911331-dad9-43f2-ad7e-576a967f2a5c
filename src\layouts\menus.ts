type MenuItem = {
  icon?: string;
  title: string;
  value: string;
  path: string;
}

type Menu = {
  icon: string;
  text: string;
  value: string;
  path: string;
  childreen: MenuItem[];
}

export const mainLinks: Menu[] = [
  {
    icon: 'dashboard',
    text: 'Tableau de bord',
    value: 'dashboard',
    path: '/',
    childreen: []
  },
  {
    icon: 'account_balance_wallet',
    text: 'État Financier',
    value: 'financial',
    path: '/financial',
    childreen: []
  },
  {
    icon: 'analytics',
    text: 'Statistiques',
    value: 'analytics',
    path: '/analytics',
    childreen: [
      {
        title: "Etat des clients",
        value: "clients",
        path: "/analytics/clients"
      },
      {
        title: "Etat des carnets",
        value: "subscriptions",
        path: "/analytics/subscriptions"
      },
      {
        title: "Etat des cotisations",
        value: "cotisations",
        path: "/analytics/cotisations"
      },
      {
        title: "Etat des versements",
        value: "versements",
        path: "/analytics/versements"
      },
      {
        title: "Etat des collecteurs",
        value: "collectors",
        path: "/analytics/collectors"
      }
    ]
  },
];

export const clientsLinks: Menu[] = [
  {
    icon: 'groups',
    text: 'Clients',
    value: 'clients',
    path: '/clients',
    childreen: []
  },
  {
    icon: 'card_giftcard',
    text: 'Carnets',
    value: 'carnets',
    path: '/carnets',
    childreen: []
  },
  {
    icon: 'account_balance',
    text: "Packs",
    value: 'packs',
    path: '/packs',
    childreen: []
  },
];

export const personnelLinks: Menu[] = [
  {
    icon: 'person',
    text: 'Agents Collecteurs',
    value: 'collectors',
    path: '/personals/collectors',
    childreen: []
  },
  {
    icon: 'supervisor_account',
    text: 'Agents Superviseurs',
    value: 'supervisors',
    path: '/personals/supervisors',
    childreen: []
  },
  {
    icon: 'account_balance',
    text: 'Agents caissiers',
    value: "cashiers",
    path: "/personals/cashiers",
    childreen: []
  },
  {
    icon: 'local_shipping',
    text: 'Agents Livreurs',
    value: 'livreurs',
    path: '/personals/livreurs',
    childreen: []
  },
  {
    icon: 'person',
    text: "Agents Chef d'agence",
    value: "agency_chiefs",
    path: "/personals/agency_chiefs",
    childreen: []
  },
];

export const financialLinks: Menu[] = [
  {
    icon: 'account_balance_wallet',
    text: 'Etat Caisses',
    value: 'accounting',
    path: '/accounting',
    childreen: []
  },
  {
    icon: 'receipt_long',
    text: 'Versements',
    value: 'deposits',
    path: '/deposits',
    childreen: []
  },
  {
    icon: 'payments',
    text: 'Cotisations',
    value: 'cotisations',
    path: '/cotisations',
    childreen: []
  },
  {
    icon: 'shield',
    text: 'Contrôles Financiers',
    value: 'controls',
    path: '/controls',
    childreen: []
  }
];

export const inventoryLinks: Menu[] = [
  {
    icon: 'inventory_2',
    text: "Produits",
    value: 'products',
    path: '/products',
    childreen: []
  },
  {
    icon: 'warehouse',
    text: 'Stocks',
    value: 'stocks',
    path: '/stocks',
    childreen: []
  },
  {
    icon: 'local_shipping',
    text: 'Livraisons',
    value: 'deliveries',
    path: '/deliveries',
    childreen: []
  },
];

export const administrationLinks: Menu[] = [
  {
    icon: 'home_work',
    text: "Agences",
    value: 'agencies',
    path: '/agencies',
    childreen: []
  },
  {
    icon: 'groups',
    text: 'Utilisateurs',
    value: 'users',
    path: '/users',
    childreen: []
  },
  {
    icon: 'map',
    text: 'Déploiement',
    value: 'localisation',
    path: '/localisation/countries',
    childreen: []
  },
];

export const settingsLinks: Menu[] = [
  {
    icon: 'settings',
    text: 'Paramètres',
    value: 'settings',
    path: '/settings',
    childreen: []
  },
  {
    icon: 'help',
    text: 'Support',
    value: 'helps',
    path: '/helps',
    childreen: []
  },
];

const menus = {
  main: mainLinks,
  clients: clientsLinks,
  personnel: personnelLinks,
  financial: financialLinks,
  inventory: inventoryLinks,
  administration: administrationLinks,
  settings: settingsLinks,
};

// Réorganisation avec titres adaptés
export const menuSections = [
  {
    title: "Principal",
    links: menus.main,
  },
  {
    title: "Gestion des clients",
    links: menus.clients,
  },
  {
    title: "Gestion du personnel",
    links: menus.personnel,
  },
  {
    title: "Gestion financière",
    links: menus.financial,
  },
  {
    title: "Gestion des stocks",
    links: menus.inventory,
  },
  {
    title: "Administration",
    links: menus.administration,
  },
  {
    title: "Paramètres",
    links: menus.settings,
  },
];

// Exports pour compatibilité avec l'ancien code
export const links1 = mainLinks;
export const links2 = personnelLinks;
export const links3 = financialLinks;
export const links4 = inventoryLinks;
export const links5 = [...administrationLinks, ...settingsLinks];
