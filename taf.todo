<template>
  <div class="financial-chart">
    <apexchart type="line" height="350" :options="chartOptions" :series="series" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import VueApexCharts from 'vue3-apexcharts';

export default defineComponent({
  name: 'FinancialChart',
  components: {
    apexchart: VueApexCharts,
  },
  props: {
    period: {
      type: String,
      default: 'week',
      validator: (value: string) => ['today', 'week', 'month', 'year'].includes(value)
    }
  },
  setup(props) {
    // Données simulées adaptées aux souscriptions et cotisations
    const financialData = ref({
      today: {
        labels: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
        subscriptions: [5, 3, 7, 12, 18, 14, 10, 8],
        cotisations: [3, 2, 5, 8, 12, 9, 7, 5],
        revenus: [250000, 150000, 420000, 750000, 1100000, 850000, 600000, 450000]
      },
      week: {
        labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
        subscriptions: [15, 12, 18, 22, 14, 20, 25],
        cotisations: [10, 8, 12, 15, 9, 14, 18],
        revenus: [1200000, 950000, 1500000, 1850000, 1150000, 1700000, 2100000]
      },
      month: {
        labels: ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4'],
        subscriptions: [85, 92, 78, 105],
        cotisations: [60, 65, 55, 75],
        revenus: [5200000, 5800000, 4900000, 6250000]
      },
      year: {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
        subscriptions: [120, 110, 130, 150, 140, 160, 180, 170, 150, 140, 130, 160],
        cotisations: [85, 75, 90, 110, 95, 115, 130, 120, 105, 95, 85, 110],
        revenus: [8500000, 7800000, 9200000, 11000000, 9500000, 11500000, 13000000, 12000000, 10500000, 9500000, 8500000, 11000000]
      }
    });

    // Séries de données calculées selon la période
    const series = computed(() => {
      const data = financialData.value[props.period as keyof typeof financialData.value];
      return [
        {
          name: 'Souscriptions',
          type: 'column',
          data: data.subscriptions
        },
        {
          name: 'Cotisations',
          type: 'area',
          data: data.cotisations
        },
        {
          name: 'Revenus (XOF)',
          type: 'line',
          data: data.revenus
        }
      ];
    });

    // Options du graphique adaptées
    const chartOptions = computed(() => {
      const data = financialData.value[props.period as keyof typeof financialData.value];

      return {
        chart: {
          height: 350,
          type: 'line',
          stacked: false,
          toolbar: {
            show: true,
            tools: {
              download: true,
              selection: false,
              zoom: true,
              zoomin: true,
              zoomout: true,
              pan: false,
              reset: true
            }
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800
          }
        },
        colors: ['#1976D2', '#42A5F5', '#66BB6A'],
        stroke: {
          width: [0, 2, 3],
          curve: 'smooth'
        },
        plotOptions: {
          bar: {
            columnWidth: '50%',
            borderRadius: 4
          }
        },
        fill: {
          opacity: [0.85, 0.25, 1],
          gradient: {
            inverseColors: false,
            shade: 'light',
            type: "vertical",
            opacityFrom: 0.85,
            opacityTo: 0.55,
            stops: [0, 100, 100, 100]
          }
        },
        labels: data.labels,
        markers: {
          size: [0, 0, 4],
          strokeColors: '#fff',
          strokeWidth: 2,
          hover: {
            size: 6
          }
        },
        xaxis: {
          type: props.period === 'today' ? 'category' : 'category',
          labels: {
            style: {
              colors: '#666',
              fontSize: '12px'
            }
          }
        },
        yaxis: [
          {
            title: {
              text: 'Souscriptions & Cotisations',
              style: {
                color: '#666'
              }
            },
            labels: {
              style: {
                colors: '#666'
              }
            }
          },
          {
            opposite: true,
            title: {
              text: 'Revenus (XOF)',
              style: {
                color: '#66BB6A'
              }
            },
            labels: {
              style: {
                colors: '#66BB6A'
              },
              formatter: function (val: number) {
                return val.toLocaleString() + ' XOF';
              }
            }
          }
        ],
        tooltip: {
          shared: true,
          intersect: false,
          y: [
            {
              formatter: function (y: number) {
                if (typeof y !== "undefined") {
                  return y.toFixed(0) + " souscriptions";
                }
                return y;
              }
            },
            {
              formatter: function (y: number) {
                if (typeof y !== "undefined") {
                  return y.toFixed(0) + " cotisations";
                }
                return y;
              }
            },
            {
              formatter: function (y: number) {
                if (typeof y !== "undefined") {
                  return y.toLocaleString() + " XOF";
                }
                return y;
              }
            }
          ]
        },
        legend: {
          position: 'top',
          horizontalAlign: 'left',
          offsetX: 40
        },
        grid: {
          borderColor: '#f1f1f1',
          strokeDashArray: 3
        }
      };
    });

    return {
      series,
      chartOptions
    };
  }
});
</script>

<style scoped>
.financial-chart {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 8px;
  padding: 16px;
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}
</style>
