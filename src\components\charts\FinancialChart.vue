<template>
  <q-card class="financial-trend-card" flat>
    <div class="row items-center q-mb-md">
      <div class="col">
        <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Historique financier</h5>
      </div>
      <div class="col-auto">
        <div class="row items-center q-gutter-sm">
          <q-select
            v-model="selectedYear"
            :options="yearOptions"
            label="Année"
            outlined
            dense
            style="min-width: 120px;"
          />
          <q-btn-dropdown
            color="primary"
            :label="getPeriodLabel(selectedPeriod)"
            size="sm"
            outline
            no-caps
          >
            <q-list>
              <q-item
                v-for="period in periodOptions"
                :key="period.value"
                clickable
                v-close-popup
                @click="selectedPeriod = period.value"
                :active="selectedPeriod === period.value"
              >
                <q-item-section>
                  <q-item-label>{{ period.label }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </div>
      </div>
    </div>

    <!-- Graphique -->
    <div style="height: 320px;">
      <apexchart
        type="line"
        height="100%"
        :options="chartOptions"
        :series="series"
      />
    </div>

    <q-inner-loading :showing="loading" />
  </q-card>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';
import VueApexCharts from 'vue3-apexcharts';
import { api } from 'boot/axios';

export default defineComponent({
  name: 'FinancialTrendCard',
  components: {
    apexchart: VueApexCharts,
  },
  setup() {
    // États
    const selectedPeriod = ref('week');
    const selectedYear = ref(new Date().getFullYear());
    const loading = ref(false);
    const chartData = ref({
      labels: [],
      subscriptions: [],
      cotisations: [],
      revenus: []
    });

    // Options
    const periodOptions = [
      { value: 'today', label: "Aujourd'hui" },
      { value: 'week', label: 'Semaine' },
      { value: 'month', label: 'Mois' },
      { value: 'year', label: 'Année' }
    ];

    const yearOptions = computed(() => {
      const currentYear = new Date().getFullYear();
      return Array.from({length: 5}, (_, i) => currentYear - i);
    });

    // Méthodes
    const getPeriodLabel = (period: string) => {
      return periodOptions.find(p => p.value === period)?.label || 'Période';
    };

    const fetchData = async () => {
      loading.value = true;
      try {
        // Simulation de données - À remplacer par un appel API réel
        chartData.value = getSimulatedData(selectedPeriod.value, selectedYear.value);

        // Exemple d'appel API (à décommenter et adapter)
        /*
        const response = await api.get('/api/financial-trends', {
          params: {
            period: selectedPeriod.value,
            year: selectedYear.value
          }
        });
        if (response.data.success) {
          chartData.value = response.data.data;
        }
        */
      } catch (error) {
        console.error('Erreur:', error);
      } finally {
        loading.value = false;
      }
    };

    // Simulation de données (à remplacer par des données réelles)
    const getSimulatedData = (period: string, year: number) => {
      const baseData = {
        today: {
          labels: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
          subscriptions: [5, 3, 7, 12, 18, 14, 10, 8],
          cotisations: [3, 2, 5, 8, 12, 9, 7, 5],
          revenus: [250000, 150000, 420000, 750000, 1100000, 850000, 600000, 450000]
        },
        week: {
          labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
          subscriptions: [15, 12, 18, 22, 14, 20, 25],
          cotisations: [10, 8, 12, 15, 9, 14, 18],
          revenus: [1200000, 950000, 1500000, 1850000, 1150000, 1700000, 2100000]
        },
        month: {
          labels: ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4'],
          subscriptions: [85, 92, 78, 105],
          cotisations: [60, 65, 55, 75],
          revenus: [5200000, 5800000, 4900000, 6250000]
        },
        year: {
          labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
          subscriptions: [120, 110, 130, 150, 140, 160, 180, 170, 150, 140, 130, 160],
          cotisations: [85, 75, 90, 110, 95, 115, 130, 120, 105, 95, 85, 110],
          revenus: [8500000, 7800000, 9200000, 11000000, 9500000, 11500000, 13000000, 12000000, 10500000, 9500000, 8500000, 11000000]
        }
      };

      return baseData[period as keyof typeof baseData] || baseData.week;
    };

    // Watchers
    watch([selectedPeriod, selectedYear], () => {
      fetchData();
    }, { immediate: true });

    // Calculs
    const series = computed(() => [
      {
        name: 'Souscriptions',
        type: 'column',
        data: chartData.value.subscriptions
      },
      {
        name: 'Cotisations',
        type: 'area',
        data: chartData.value.cotisations
      },
      {
        name: 'Revenus (XOF)',
        type: 'line',
        data: chartData.value.revenus
      }
    ]);

    const chartOptions = computed(() => ({
      chart: {
        type: 'line',
        toolbar: {
          show: true,
          tools: {
            download: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: false,
            reset: true
          }
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      colors: ['#1976D2', '#42A5F5', '#66BB6A'],
      stroke: {
        width: [0, 2, 3],
        curve: 'smooth'
      },
      plotOptions: {
        bar: {
          columnWidth: '50%',
          borderRadius: 4
        }
      },
      fill: {
        opacity: [0.85, 0.25, 1]
      },
      labels: chartData.value.labels,
      xaxis: {
        type: 'category',
        labels: {
          style: {
            colors: '#666',
            fontSize: '12px'
          }
        }
      },
      yaxis: [
        {
          title: { text: 'Quantité', style: { color: '#666' } },
          labels: { style: { colors: '#666' } }
        },
        {
          opposite: true,
          title: {
            text: 'Revenus (XOF)',
            style: { color: '#66BB6A' }
          },
          labels: {
            style: { colors: '#66BB6A' },
            formatter: (val: number) => val.toLocaleString() + ' XOF'
          }
        }
      ],
      tooltip: {
        shared: true,
        y: [
          { formatter: (y: number) => y?.toFixed(0) + ' souscriptions' },
          { formatter: (y: number) => y?.toFixed(0) + ' cotisations' },
          { formatter: (y: number) => y?.toLocaleString() + ' XOF' }
        ]
      },
      legend: {
        position: 'top',
        horizontalAlign: 'left'
      },
      grid: {
        borderColor: '#f1f1f1'
      }
    }));

    return {
      selectedPeriod,
      selectedYear,
      periodOptions,
      yearOptions,
      loading,
      series,
      chartOptions,
      getPeriodLabel
    };
  }
});
</script>

<style scoped>
.financial-trend-card {
  height: 100%;
  padding: 16px;
}

.financial-trend-card .q-card__section {
  padding: 0;
}
</style>
